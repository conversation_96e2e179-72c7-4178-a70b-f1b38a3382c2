#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

/* Custom styles for react-datepicker */
.react-datepicker-wrapper {
  width: 100% !important;
  display: block !important;
}

.react-datepicker__input-container {
  width: 100% !important;
  display: block !important;
}

.react-datepicker__input-container input {
  width: 100% !important;
  display: block !important;
}

.react-datepicker-popper {
  z-index: 9999 !important;
}

.react-datepicker {
  font-family: inherit;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  z-index: 9999;
}

.react-datepicker__header {
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  border-radius: 0.5rem 0.5rem 0 0;
}

.react-datepicker__current-month {
  font-weight: 600;
  color: #374151;
}

.react-datepicker__day-name {
  color: #6b7280;
  font-weight: 500;
}

.react-datepicker__day {
  color: #374151;
  border-radius: 0.25rem;
  margin: 0.125rem;
}

.react-datepicker__day--selected {
  background-color: #3b82f6 !important;
  color: white !important;
  border-radius: 0.25rem;
}

.react-datepicker__day--selected:hover {
  background-color: #2563eb !important;
}

.react-datepicker__day:hover {
  background-color: #dbeafe;
  border-radius: 0.25rem;
}

.react-datepicker__day--today {
  background-color: #fef3c7;
  color: #92400e;
  font-weight: 600;
}

.react-datepicker__day--keyboard-selected {
  background-color: #e0e7ff;
  color: #3730a3;
}

/* Ensure date picker input is visible */
.react-datepicker__input-container input {
  background-color: white !important;
  border: 1px solid #d1d5db !important;
  color: #374151 !important;
}

.react-datepicker__input-container input:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* Ensure date picker works in table cells */
.react-datepicker__tab-loop {
  position: absolute;
}

.react-datepicker__portal {
  position: fixed !important;
  z-index: 9999 !important;
}

/* Override any table cell overflow that might hide the calendar */
td .react-datepicker-wrapper {
  position: relative;
  z-index: 10;
}

td .react-datepicker-popper {
  z-index: 9999 !important;
  position: fixed !important;
}

/* Make sure the input is clickable and read-only styling */
.react-datepicker__input-container input {
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* Style read-only date inputs */
.react-datepicker__input-container input[readonly] {
  cursor: pointer !important;
  background-color: #f9fafb !important;
  user-select: none !important;
}

.react-datepicker__input-container input[readonly]:focus {
  background-color: #f3f4f6 !important;
}

/* Frequency modal specific styles */
.frequency-modal {
  z-index: 9999 !important;
}

/* Ensure frequency modal date pickers have higher z-index */
.frequency-modal .react-datepicker-popper {
  z-index: 10000 !important;
}

.frequency-modal .react-datepicker {
  z-index: 10000 !important;
}

/* Day End Modal and Report Modal styles */
.day-end-modal, .day-end-report-modal {
  z-index: 9999 !important;
}

.day-end-modal .react-datepicker-popper,
.day-end-report-modal .react-datepicker-popper {
  z-index: 10000 !important;
}

/* Report View styles */
.report-view {
  background-color: #f9fafb;
}

.report-view table {
  font-size: 0.875rem;
}

.report-view .date-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

/* Enhanced date picker styles for better visibility */
.react-datepicker__day--in-selecting-range {
  background-color: #dbeafe !important;
  color: #1e40af !important;
}

.react-datepicker__day--in-range {
  background-color: #bfdbfe !important;
  color: #1e40af !important;
}

.react-datepicker__day--range-start,
.react-datepicker__day--range-end {
  background-color: #3b82f6 !important;
  color: white !important;
}

/* Improved modal backdrop */
.modal-backdrop {
  backdrop-filter: blur(2px);
  background-color: rgba(0, 0, 0, 0.5);
}

/* Time input styling */
.time-input {
  text-align: center;
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

/* Status and priority badges in report view */
.status-badge, .priority-badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

/* Report table improvements */
.report-view table {
  table-layout: fixed;
  width: 100%;
}

.report-view th,
.report-view td {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.report-view .truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Ensure consistent column widths */
.report-view table th:nth-child(1),
.report-view table td:nth-child(1) { width: 8rem; }   /* Client */
.report-view table th:nth-child(2),
.report-view table td:nth-child(2) { width: 10rem; }  /* Task */
.report-view table th:nth-child(3),
.report-view table td:nth-child(3) { width: 10rem; }  /* Sub Task */
.report-view table th:nth-child(4),
.report-view table td:nth-child(4) { width: 8rem; }   /* Allotted To */
.report-view table th:nth-child(5),
.report-view table td:nth-child(5) { width: 6rem; }   /* Team Leader */
.report-view table th:nth-child(6),
.report-view table td:nth-child(6) { width: 6rem; }   /* Priority */
.report-view table th:nth-child(7),
.report-view table td:nth-child(7) { width: 7rem; }   /* Status */
.report-view table th:nth-child(8),
.report-view table td:nth-child(8) { width: 7rem; }   /* Due Date */
.report-view table th:nth-child(9),
.report-view table td:nth-child(9) { width: 5rem; }   /* ET */
.report-view table th:nth-child(10),
.report-view table td:nth-child(10) { width: 5rem; }  /* Time Taken */
.report-view table th:nth-child(11),
.report-view table td:nth-child(11) { width: 12rem; } /* Comment */

/* Center align numeric columns */
.report-view table th:nth-child(9),
.report-view table td:nth-child(9),
.report-view table th:nth-child(10),
.report-view table td:nth-child(10) {
  text-align: center;
}

/* Improve table readability */
.report-view tbody tr:nth-child(even) {
  background-color: #f9fafb;
}

.report-view tbody tr:hover {
  background-color: #f3f4f6 !important;
}
