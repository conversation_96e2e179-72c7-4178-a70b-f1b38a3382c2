#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

/* Custom styles for react-datepicker */
.react-datepicker-wrapper {
  width: 100% !important;
  display: block !important;
}

.react-datepicker__input-container {
  width: 100% !important;
  display: block !important;
}

.react-datepicker__input-container input {
  width: 100% !important;
  display: block !important;
}

.react-datepicker-popper {
  z-index: 9999 !important;
}

.react-datepicker {
  font-family: inherit;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  z-index: 9999;
}

.react-datepicker__header {
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  border-radius: 0.5rem 0.5rem 0 0;
}

.react-datepicker__current-month {
  font-weight: 600;
  color: #374151;
}

.react-datepicker__day-name {
  color: #6b7280;
  font-weight: 500;
}

.react-datepicker__day {
  color: #374151;
  border-radius: 0.25rem;
  margin: 0.125rem;
}

.react-datepicker__day--selected {
  background-color: #3b82f6 !important;
  color: white !important;
  border-radius: 0.25rem;
}

.react-datepicker__day--selected:hover {
  background-color: #2563eb !important;
}

.react-datepicker__day:hover {
  background-color: #dbeafe;
  border-radius: 0.25rem;
}

.react-datepicker__day--today {
  background-color: #fef3c7;
  color: #92400e;
  font-weight: 600;
}

.react-datepicker__day--keyboard-selected {
  background-color: #e0e7ff;
  color: #3730a3;
}

/* Ensure date picker input is visible */
.react-datepicker__input-container input {
  background-color: white !important;
  border: 1px solid #d1d5db !important;
  color: #374151 !important;
}

.react-datepicker__input-container input:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* Ensure date picker works in table cells */
.react-datepicker__tab-loop {
  position: absolute;
}

.react-datepicker__portal {
  position: fixed !important;
  z-index: 9999 !important;
}

/* Override any table cell overflow that might hide the calendar */
td .react-datepicker-wrapper {
  position: relative;
  z-index: 10;
}

td .react-datepicker-popper {
  z-index: 9999 !important;
  position: fixed !important;
}

/* Make sure the input is clickable and read-only styling */
.react-datepicker__input-container input {
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* Style read-only date inputs */
.react-datepicker__input-container input[readonly] {
  cursor: pointer !important;
  background-color: #f9fafb !important;
  user-select: none !important;
}

.react-datepicker__input-container input[readonly]:focus {
  background-color: #f3f4f6 !important;
}

/* Frequency modal specific styles */
.frequency-modal {
  z-index: 9999 !important;
}

/* Ensure frequency modal date pickers have higher z-index */
.frequency-modal .react-datepicker-popper {
  z-index: 10000 !important;
}

.frequency-modal .react-datepicker {
  z-index: 10000 !important;
}

/* Day End Modal and Report Modal styles */
.day-end-modal, .day-end-report-modal {
  z-index: 9999 !important;
}

.day-end-modal .react-datepicker-popper,
.day-end-report-modal .react-datepicker-popper {
  z-index: 10000 !important;
}

/* Report View styles */
.report-view {
  background-color: #f9fafb;
}

.report-view table {
  font-size: 0.875rem;
}

.report-view .date-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

/* Enhanced date picker styles for better visibility */
.react-datepicker__day--in-selecting-range {
  background-color: #dbeafe !important;
  color: #1e40af !important;
}

.react-datepicker__day--in-range {
  background-color: #bfdbfe !important;
  color: #1e40af !important;
}

.react-datepicker__day--range-start,
.react-datepicker__day--range-end {
  background-color: #3b82f6 !important;
  color: white !important;
}

/* Improve date picker keyboard navigation */
.react-datepicker__day {
  cursor: pointer;
  outline: none;
}

.react-datepicker__day:focus {
  background-color: #3b82f6 !important;
  color: white !important;
  outline: 2px solid #1e40af;
  outline-offset: -2px;
}

.react-datepicker__day--keyboard-selected {
  background-color: #3b82f6 !important;
  color: white !important;
}

.react-datepicker__day--selected:focus {
  background-color: #1e40af !important;
  color: white !important;
}

/* Ensure calendar is focusable */
.react-datepicker {
  outline: none;
}

.react-datepicker__month-container {
  outline: none;
}

/* Improved modal backdrop */
.modal-backdrop {
  backdrop-filter: blur(2px);
  background-color: rgba(0, 0, 0, 0.5);
}

/* Time input styling */
.time-input {
  text-align: center;
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

/* Status and priority badges in report view */
.status-badge, .priority-badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

/* Report table improvements */
.report-view table {
  border-collapse: collapse;
  width: 100%;
  font-size: 0.875rem;
}

.report-view th,
.report-view td {
  border: 1px solid #d1d5db;
  word-wrap: break-word;
  overflow-wrap: break-word;
  vertical-align: top;
}

.report-view .truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* Improve table readability */
.report-view tbody tr:nth-child(even) {
  background-color: #f9fafb;
}

.report-view tbody tr:hover {
  background-color: #f3f4f6 !important;
}

/* Table header styling */
.report-view thead th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
  position: sticky;
  top: 0;
  z-index: 10;
}

/* Ensure proper spacing and alignment */
.report-view table td,
.report-view table th {
  padding: 8px 12px;
  text-align: left;
}

/* Center align specific columns */
.report-view table th:nth-child(9),
.report-view table td:nth-child(9),
.report-view table th:nth-child(10),
.report-view table td:nth-child(10) {
  text-align: center;
}
