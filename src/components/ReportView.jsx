import React, { useMemo } from 'react'
import { HistoryService } from '../services/historyService'

const ReportView = ({ reports, startDate, endDate, onClose }) => {
  // Group reports by date
  const groupedReports = useMemo(() => {
    const grouped = {}
    
    reports.forEach(report => {
      const date = report.date
      if (!grouped[date]) {
        grouped[date] = []
      }
      grouped[date].push(report)
    })
    
    // Sort dates in descending order
    const sortedDates = Object.keys(grouped).sort((a, b) => new Date(b) - new Date(a))
    
    return sortedDates.map(date => ({
      date,
      reports: grouped[date].sort((a, b) => new Date(b.createdAt?.toDate?.() || b.createdAt) - new Date(a.createdAt?.toDate?.() || a.createdAt))
    }))
  }, [reports])

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-GB', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatTime = (timestamp) => {
    if (!timestamp) return 'N/A'
    
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp)
    return date.toLocaleTimeString('en-GB', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'Completed':
        return 'bg-green-100 text-green-800'
      case 'In Progress':
        return 'bg-blue-100 text-blue-800'
      case 'Todo':
        return 'bg-gray-100 text-gray-800'
      case 'Archived':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'Critical':
        return 'bg-red-100 text-red-800'
      case 'High':
        return 'bg-orange-100 text-orange-800'
      case 'Medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'Low':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="fixed inset-0 bg-white z-50 overflow-auto">
      {/* Header */}
      <div className="bg-white shadow-sm border-b sticky top-0 z-10">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Day End Report</h1>
              <p className="text-sm text-gray-600 mt-1">
                {formatDate(startDate)} - {formatDate(endDate)}
              </p>
            </div>
            <button
              onClick={onClose}
              className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md font-medium transition-colors"
            >
              Close Report
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="px-6 py-6">
        {groupedReports.length === 0 ? (
          <div className="text-center py-12">
            <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Data Found</h3>
            <p className="text-gray-600">No day-end snapshots found for the selected date range.</p>
          </div>
        ) : (
          <div className="space-y-8">
            {groupedReports.map(({ date, reports: dateReports }) => (
              <div key={date} className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                {/* Date Header */}
                <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <h2 className="text-lg font-semibold text-gray-900">
                      {formatDate(date)}
                    </h2>
                    <div className="text-sm text-gray-600">
                      {dateReports.length} snapshot{dateReports.length !== 1 ? 's' : ''}
                    </div>
                  </div>
                </div>

                {/* Reports for this date */}
                <div className="divide-y divide-gray-200">
                  {dateReports.map((report, reportIndex) => (
                    <div key={report.id} className="p-6">
                      {/* Report Header */}
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-4">
                          <h3 className="text-md font-medium text-gray-900">
                            Snapshot #{reportIndex + 1}
                          </h3>
                          <span className="text-sm text-gray-500">
                            Time: {formatTime(report.createdAt)}
                          </span>
                          {report.timeSpent && (
                            <span className="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">
                              Duration: {HistoryService.formatTimeSpent(report.timeSpent)}
                            </span>
                          )}
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <span>Total: {report.totalTasks}</span>
                          <span className="text-green-600">Completed: {report.completedTasks}</span>
                          <span className="text-blue-600">In Progress: {report.inProgressTasks}</span>
                          <span className="text-gray-600">Todo: {report.todoTasks}</span>
                        </div>
                      </div>

                      {/* Tasks Table */}
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Task</th>
                              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sub Task</th>
                              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Allotted To</th>
                              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time Taken</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {report.tasks.map((task, taskIndex) => (
                              <tr key={taskIndex} className="hover:bg-gray-50">
                                <td className="px-3 py-2 text-sm text-gray-900">{task.client || '-'}</td>
                                <td className="px-3 py-2 text-sm text-gray-900">{task.task || '-'}</td>
                                <td className="px-3 py-2 text-sm text-gray-900">{task.subTask || '-'}</td>
                                <td className="px-3 py-2 text-sm text-gray-900">{task.allottedTo || '-'}</td>
                                <td className="px-3 py-2 text-sm">
                                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(task.priority)}`}>
                                    {task.priority || 'Medium'}
                                  </span>
                                </td>
                                <td className="px-3 py-2 text-sm">
                                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(task.status)}`}>
                                    {task.status || 'Todo'}
                                  </span>
                                </td>
                                <td className="px-3 py-2 text-sm text-gray-900">
                                  {task.dueDate ? new Date(task.dueDate).toLocaleDateString('en-GB') : '-'}
                                </td>
                                <td className="px-3 py-2 text-sm text-gray-900">{task.timeTaken || 0}h</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default ReportView
