import {
  collection,
  addDoc,
  getDocs,
  query,
  where,
  orderBy,
  serverTimestamp,
  doc,
  getDoc
} from 'firebase/firestore'
import { db, COLLECTIONS } from './firebase'

// History Service for managing task history and day-end reports
export class HistoryService {
  // Save day-end snapshot of tasks
  static async saveDayEndSnapshot(tasks, selectedDate, timeSpent, currentUserEmail) {
    try {
      const snapshot = {
        date: selectedDate,
        timeSpent: timeSpent, // Format: { hours: 0, minutes: 0, seconds: 0 }
        tasks: tasks.map(task => ({
          id: task.id,
          client: task.client,
          task: task.task,
          subTask: task.subTask,
          estimatedTime: task.estimatedTime,
          allottedTo: task.allottedTo,
          teamLeader: task.teamLeader,
          priority: task.priority,
          dueDate: task.dueDate,
          frequency: task.frequency,
          comment: task.comment,
          status: task.status,
          timeTaken: task.timeTaken
        })),
        createdBy: currentUserEmail.toLowerCase(),
        createdAt: serverTimestamp(),
        totalTasks: tasks.length,
        completedTasks: tasks.filter(task => task.status === 'Completed').length,
        inProgressTasks: tasks.filter(task => task.status === 'In Progress').length,
        todoTasks: tasks.filter(task => task.status === 'Todo').length
      }

      const docRef = await addDoc(collection(db, COLLECTIONS.TASK_HISTORY), snapshot)
      return docRef.id
    } catch (error) {
      console.error('Error saving day-end snapshot:', error)
      throw error
    }
  }

  // Get day-end snapshots for a date range
  static async getDayEndReports(startDate, endDate, currentUserEmail) {
    try {
      const q = query(
        collection(db, COLLECTIONS.TASK_HISTORY),
        where('createdBy', '==', currentUserEmail.toLowerCase()),
        where('date', '>=', startDate),
        where('date', '<=', endDate),
        orderBy('date', 'desc')
      )

      const querySnapshot = await getDocs(q)
      const reports = []
      
      querySnapshot.forEach((doc) => {
        reports.push({
          id: doc.id,
          ...doc.data()
        })
      })

      return reports
    } catch (error) {
      console.error('Error fetching day-end reports:', error)
      throw error
    }
  }

  // Get specific day-end snapshot
  static async getDayEndSnapshot(snapshotId) {
    try {
      const docRef = doc(db, COLLECTIONS.TASK_HISTORY, snapshotId)
      const docSnap = await getDoc(docRef)
      
      if (docSnap.exists()) {
        return {
          id: docSnap.id,
          ...docSnap.data()
        }
      } else {
        throw new Error('Snapshot not found')
      }
    } catch (error) {
      console.error('Error fetching day-end snapshot:', error)
      throw error
    }
  }

  // Get all snapshots for a specific date
  static async getSnapshotsForDate(date, currentUserEmail) {
    try {
      const q = query(
        collection(db, COLLECTIONS.TASK_HISTORY),
        where('createdBy', '==', currentUserEmail.toLowerCase()),
        where('date', '==', date),
        orderBy('createdAt', 'desc')
      )

      const querySnapshot = await getDocs(q)
      const snapshots = []
      
      querySnapshot.forEach((doc) => {
        snapshots.push({
          id: doc.id,
          ...doc.data()
        })
      })

      return snapshots
    } catch (error) {
      console.error('Error fetching snapshots for date:', error)
      throw error
    }
  }

  // Format time for display
  static formatTimeSpent(timeSpent) {
    if (!timeSpent) return '00:00:00'
    
    const { hours = 0, minutes = 0, seconds = 0 } = timeSpent
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  }

  // Parse time string to object
  static parseTimeString(timeString) {
    const parts = timeString.split(':')
    return {
      hours: parseInt(parts[0] || 0, 10),
      minutes: parseInt(parts[1] || 0, 10),
      seconds: parseInt(parts[2] || 0, 10)
    }
  }

  // Calculate total time spent across multiple snapshots
  static calculateTotalTime(snapshots) {
    let totalSeconds = 0
    
    snapshots.forEach(snapshot => {
      if (snapshot.timeSpent) {
        const { hours = 0, minutes = 0, seconds = 0 } = snapshot.timeSpent
        totalSeconds += (hours * 3600) + (minutes * 60) + seconds
      }
    })

    const hours = Math.floor(totalSeconds / 3600)
    const minutes = Math.floor((totalSeconds % 3600) / 60)
    const seconds = totalSeconds % 60

    return { hours, minutes, seconds }
  }
}
